import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/base.dart';
import 'package:flutter_kit/src/base/logic/view_state_paging_logic.dart';
import 'package:flutter_kit/src/datasource/models/api_response/unified_response.dart';
import 'package:flutter_kit/src/datasource/models/job_info_entity.dart';
import '../../../datasource/repositories/home_repository.dart';

class HomeLogic extends ViewStateLogic {
  final HomeReportsitory repository;
  List<JobInfoEntity> _jobs = [];
  VoidCallback? onRefreshSuccess;
  VoidCallback? onRefreshStart;
  VoidCallback? onRefreshEnd;

  List<JobInfoEntity> get jobs => _jobs;

  HomeLogic({required this.repository}) {

  }



  @override
  void loadData() {
    refreshPaging();
  }

  @override
  void refreshPaging() {
    // 如果已有数据，不改变ViewState，只在后台刷新
    final bool hasExistingData = _jobs.isNotEmpty;

    // 如果有数据，触发刷新开始回调（显示顶部loading）
    if (hasExistingData && onRefreshStart != null) {
      onRefreshStart!();
    }

    sendRequest<List<JobInfoEntity>>(
      bindViewState: !hasExistingData, // 有数据时不绑定ViewState
      successCallback: (data) {
        _jobs = data ?? [];
        notifyListeners();

        // 触发刷新结束回调
        if (onRefreshEnd != null) {
          onRefreshEnd!();
        }

        // 如果有数据，显示刷新成功提示
        if (_jobs.isNotEmpty && onRefreshSuccess != null) {
          onRefreshSuccess!();
        }
      },
      failCallback: () {
        // 失败时也要结束loading
        if (onRefreshEnd != null) {
          onRefreshEnd!();
        }
      }
    );
  }




  Future<void> fetchJobs() async {
    loadData();
  }
}
