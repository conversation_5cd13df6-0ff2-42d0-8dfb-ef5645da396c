import 'package:flutter/foundation.dart';
import 'package:flutter_kit/src/core/performance/shader_warm_up.dart';
import 'package:flutter_kit/src/core/performance/performance_monitor.dart';

/// 🎯 性能优化服务
/// 
/// 功能特性：
/// - ✅ 统一的性能优化入口
/// - ✅ 着色器预热管理
/// - ✅ 性能监控控制
/// - ✅ 优化状态跟踪
class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  // 优化状态
  bool _isShaderWarmedUp = false;
  bool _isMonitoringStarted = false;
  bool _isFullyOptimized = false;

  /// 检查着色器是否已预热
  bool get isShaderWarmedUp => _isShaderWarmedUp;

  /// 检查性能监控是否已启动
  bool get isMonitoringStarted => _isMonitoringStarted;

  /// 检查是否完全优化
  bool get isFullyOptimized => _isFullyOptimized;

  /// 执行完整的性能优化
  Future<void> optimize() async {
    if (_isFullyOptimized) {
      debugPrint('🎯 PerformanceService - 性能优化已完成，跳过');
      return;
    }

    debugPrint('🎯 PerformanceService - 开始性能优化...');

    try {
      // 1. 启动性能监控
      await startMonitoring();

      // 2. 执行着色器预热
      await warmUpShaders();

      _isFullyOptimized = true;
      debugPrint('✅ PerformanceService - 性能优化完成');
    } catch (e) {
      debugPrint('❌ PerformanceService - 性能优化失败: $e');
    }
  }

  /// 启动性能监控
  Future<void> startMonitoring() async {
    if (_isMonitoringStarted) return;

    try {
      if (kDebugMode) {
        PerformanceMonitor().startMonitoring();
        _isMonitoringStarted = true;
        debugPrint('✅ PerformanceService - 性能监控已启动');
      }
    } catch (e) {
      debugPrint('❌ PerformanceService - 性能监控启动失败: $e');
    }
  }

  /// 停止性能监控
  void stopMonitoring() {
    if (!_isMonitoringStarted) return;

    try {
      PerformanceMonitor().stopMonitoring();
      _isMonitoringStarted = false;
      debugPrint('🎯 PerformanceService - 性能监控已停止');
    } catch (e) {
      debugPrint('❌ PerformanceService - 性能监控停止失败: $e');
    }
  }

  /// 执行着色器预热
  Future<void> warmUpShaders() async {
    if (_isShaderWarmedUp) {
      debugPrint('🔥 PerformanceService - 着色器已预热，跳过');
      return;
    }

    try {
      debugPrint('🔥 PerformanceService - 开始着色器预热...');

      // 基础着色器预热
      await AppShaderWarmUp.warmUp();
      debugPrint('✅ PerformanceService - 基础着色器预热完成');

      // 特定场景着色器预热
      await Future.wait([
        AppShaderWarmUp.warmUpKeyboardAnimation(),
        AppShaderWarmUp.warmUpListScrollShaders(),
      ]);

      _isShaderWarmedUp = true;
      debugPrint('✅ PerformanceService - 所有着色器预热完成');
    } catch (e) {
      debugPrint('❌ PerformanceService - 着色器预热失败: $e');
    }
  }

  /// 预热键盘动画着色器（单独调用）
  Future<void> warmUpKeyboard() async {
    try {
      await AppShaderWarmUp.warmUpKeyboardAnimation();
      debugPrint('✅ PerformanceService - 键盘着色器预热完成');
    } catch (e) {
      debugPrint('❌ PerformanceService - 键盘着色器预热失败: $e');
    }
  }

  /// 预热列表滚动着色器（单独调用）
  Future<void> warmUpListScroll() async {
    try {
      await AppShaderWarmUp.warmUpListScrollShaders();
      debugPrint('✅ PerformanceService - 列表滚动着色器预热完成');
    } catch (e) {
      debugPrint('❌ PerformanceService - 列表滚动着色器预热失败: $e');
    }
  }

  /// 记录性能事件
  void recordEvent(String eventName, {Map<String, dynamic>? data}) {
    if (_isMonitoringStarted) {
      PerformanceMonitor().recordEvent(eventName, data: data);
    }
  }

  /// 开始性能追踪
  void startTrace(String traceName) {
    if (_isMonitoringStarted) {
      PerformanceMonitor().startTrace(traceName);
    }
  }

  /// 结束性能追踪
  void endTrace(String traceName) {
    if (_isMonitoringStarted) {
      PerformanceMonitor().endTrace(traceName);
    }
  }

  /// 获取当前性能状态
  PerformanceStatus get currentStatus {
    if (_isMonitoringStarted) {
      return PerformanceMonitor().currentStatus;
    }
    return PerformanceStatus.unknown;
  }

  /// 重置所有状态（用于测试）
  void reset() {
    _isShaderWarmedUp = false;
    _isMonitoringStarted = false;
    _isFullyOptimized = false;
    
    if (_isMonitoringStarted) {
      PerformanceMonitor().reset();
    }
    
    AppShaderWarmUp.reset();
    debugPrint('🔄 PerformanceService - 状态已重置');
  }

  /// 获取优化状态报告
  Map<String, dynamic> getStatusReport() {
    return {
      'isShaderWarmedUp': _isShaderWarmedUp,
      'isMonitoringStarted': _isMonitoringStarted,
      'isFullyOptimized': _isFullyOptimized,
      'currentPerformanceStatus': currentStatus.toString(),
    };
  }

  /// 打印状态报告
  void printStatusReport() {
    final report = getStatusReport();
    debugPrint('📊 PerformanceService - 状态报告:');
    report.forEach((key, value) {
      debugPrint('   $key: $value');
    });
  }
}

/// 性能优化扩展方法
extension PerformanceServiceExtension on PerformanceService {
  /// 快速优化（仅着色器预热）
  Future<void> quickOptimize() async {
    await warmUpShaders();
  }

  /// 延迟优化（在指定延迟后执行）
  Future<void> delayedOptimize(Duration delay) async {
    await Future.delayed(delay);
    await optimize();
  }
}
