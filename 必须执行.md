# 必须执行的工作规则

## 核心原则

### 0. 严格遵循项目架构
- **🚨 强制要求：必须使用项目既定架构生成代码**
- **禁止使用 setState**：必须使用 Logic + ChangeNotifier 模式
- **禁止直接在 Widget 中写业务逻辑**：业务逻辑必须在 Logic 层处理
- **必须继承项目基类**：
  - UI 组件继承 `ViewStateWidget<T extends ViewStateLogic>`
  - 业务逻辑继承 `ViewStateLogic` 或 `ViewStatePagingLogic`
  - 数据模型使用 `json_serializable` 注解
- **必须使用项目依赖注入**：通过 `GetIt` 注册和获取服务
- **必须使用项目路由**：通过 `AutoRoute` 进行页面导航
- **必须遵循分层架构**：
  - Presentation Layer (UI层)
  - Business Layer (业务层)
  - Data Layer (数据层)
  - Core Layer (核心层)
- **不得偏离既定技术栈**：
  - 状态管理：Provider + ChangeNotifier
  - 路由管理：AutoRoute
  - 依赖注入：GetIt
  - 网络请求：Dio
  - 本地存储：SharedPreferences

### 1. 不创建示例代码

- 不需要创建使用示例
- 不需要创建演示代码
- 专注于实际功能实现

### 2. 不创建文档文件

- 不需要创建 MD 文件（除非明确要求）
- 不需要创建说明文档
- 不需要创建 README 文件

### 3. 项目变更日志记录

- **🚨 强制要求：项目有任何改动必须写日志**
- 每条日志不超过100字
- 日志必须写在单独的 `日志.md` 文件中
- 日志格式：`## YYYY-MM-DD HH:mm - [类型] 简要描述`
- 类型包括：新增、修改、修复、优化、重构等
- 必须记录改动的文件路径和主要变更内容

### 4. 任务管理流程

- **必须先创建任务列表**
- **不要自动执行任务**
- **等待明确指示才执行**
- 使用任务管理工具组织工作

## 工作流程

1. 分析用户需求
2. 创建详细任务列表
3. 等待用户指示
4. 按指示执行具体任务
5. 更新任务状态

## 重要提醒

**每次对话开始时都要遵循这些规则，无例外。**

## 脚本命令

### 更新 app_rouer.gr.dart

- dart run build_runner build

## 项目架构

### 技术栈选型

```text
核心框架: Flutter 3.0+
状态管理: Provider + ChangeNotifier
路由管理: AutoRoute
依赖注入: GetIt
网络请求: Dio
本地存储: SharedPreferences
屏幕适配: flutter_screenutil
国际化: flutter_localizations + intl
代码生成: build_runner + json_serializable
```

### 采用架构方案

### 整体架构设计

#### 分层架构

```text
┌─────────────────────────────────────┐
│           Presentation Layer        │  UI层
│  ┌─────────────┐ ┌─────────────────┐ │
│  │   Pages     │ │   Widgets       │ │
│  │             │ │                 │ │
│  └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│            Business Layer           │  业务层
│  ┌─────────────┐ ┌─────────────────┐ │
│  │ Controllers │ │  View Models    │ │
│  │             │ │                 │ │
│  └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│             Data Layer              │  数据层
│  ┌─────────────┐ ┌─────────────────┐ │
│  │Repositories │ │   Data Sources  │ │
│  │             │ │                 │ │
│  └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│            Core Layer               │  核心层
│  ┌─────────────┐ ┌─────────────────┐ │
│  │   Services  │ │    Utilities    │ │
│  │             │ │                 │ │
│  └─────────────┘ └─────────────────┘ │
└─────────────────────────────────────┘


## 📁 目前正在使用的架构和模块

### 目录结构

```text
lib/
├── src/
│   ├── base/                    # 基础架构层 ✅
│   │   ├── core/               # 核心状态定义
│   │   │   ├── view_state.dart          ✅ 已实现
│   │   │   ├── base_result.dart         ✅ 已实现
│   │   │   └── exceptions.dart          🔄 待完善
│   │   ├── logic/              # 业务逻辑基类
│   │   │   ├── view_state_logic.dart    ✅ 已实现
│   │   │   └── view_state_paging_logic.dart ✅ 已实现
│   │   ├── widgets/            # UI组件基类
│   │   │   ├── view_state_widget.dart   ✅ 已重构
│   │   │   ├── state_widget_builder.dart ✅ 已实现
│   │   │   └── view_state_paging_widget.dart ✅ 已实现
│   │   └── l10n/               # 国际化支持
│   │       └── base_localizations.dart  ✅ 已实现
│   ├── shared/                 # 共享组件层
│   │   ├── components/         # 通用UI组件
│   │   │   ├── app_bars/       # AppBar组件 🔄 待完善
│   │   │   ├── buttons/        # 按钮组件 ❌ 待实现
│   │   │   ├── forms/          # 表单组件 🔄 部分实现
│   │   │   └── cards/          # 卡片组件 ❌ 待实现
│   │   ├── extensions/         # 扩展方法 🔄 部分实现
│   │   ├── services/           # 共享服务
│   │   │   ├── storage/        ✅ 已实现
│   │   │   ├── logger/         ✅ 已实现
│   │   │   └── analytics/      ❌ 待实现
│   │   └── locator.dart        # 依赖注入配置 ✅ 已重构
│   ├── core/                   # 核心配置层
│   │   ├── theme/              # 主题配置 🔄 部分实现
│   │   ├── routing/            # 路由配置 ✅ 已实现
│   │   ├── application.dart    # 应用入口 ✅ 已实现
│   │   └── app_initializer.dart # 初始化配置 ✅ 已实现
│   ├── datasource/             # 数据源层
│   │   ├── http/               # 网络请求 ✅ 已实现
│   │   ├── models/             # 数据模型 🔄 部分实现
│   │   ├── repositories/       # 数据仓库 ✅ 已实现
│   │   └── local/              # 本地数据源 ❌ 待实现
│   └── features/               # 功能模块层
│       ├── home/               # 首页模块 ✅ 已实现
│       ├── login/              # 登录模块 🔄 部分实现
│       ├── jobDetail/          # 职位详情 🔄 部分实现
│       └── [其他模块]           # 待扩展
└── main.dart                   # 应用入口 ✅ 已实现
```

## 使用文档

### 创建新模块

1. **创建 Screen 页面**
   - 必须继承 `ViewStateWidget<T extends ViewStateLogic>`
   - 在 `build` 方法中使用 `StateWidgetBuilder` 构建UI
   - 通过 `logic` 属性访问业务逻辑

2. **创建 Logic 业务逻辑**
   - 必须继承 `ViewStateLogic` 或 `ViewStatePagingLogic`
   - 使用 `ChangeNotifier` 进行状态管理
   - 通过 `setLoading()`, `setSuccess()`, `setError()` 等方法更新状态
   - 使用 `notifyListeners()` 通知UI更新

3. **创建 Model 数据模型**
   - 使用 `json_serializable` 注解
   - 实现 `fromJson` 和 `toJson` 方法
   - 运行 `dart run build_runner build` 生成代码

4. **注册依赖注入**
   - 在 `locator.dart` 中注册服务
   - 使用 `GetIt.instance.registerLazySingleton()` 注册
   - 在需要的地方通过 `locator<T>()` 获取

5. **配置路由**
   - 在 `app_router.dart` 中添加路由配置
   - 使用 `@AutoRoute` 注解
   - 运行 `dart run build_runner build` 生成路由代码

### 代码示例模板

#### Screen 模板

```dart
class ExampleScreen extends ViewStateWidget<ExampleLogic> {
  const ExampleScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('示例页面')),
      body: StateWidgetBuilder<ExampleLogic>(
        builder: (context, logic, child) {
          return logic.buildStateWidget(
            context: context,
            child: YourContentWidget(),
          );
        },
      ),
    );
  }
}
```

#### Logic 模板

```dart
class ExampleLogic extends ViewStateLogic {
  // 业务逻辑实现

  @override
  void initState() {
    super.initState();
    loadData();
  }

  Future<void> loadData() async {
    try {
      setLoading();
      // 数据加载逻辑
      setSuccess();
    } catch (e) {
      setError(e.toString());
    }
  }
}
```